Sourcing setup_env.sh from hailo-rpi5-examples...
Setting up the environment...
Setting up the environment for hailo-tappas-core...
TAPPAS_VERSION is 3.31.0. Proceeding...
You are not in the venv_hailo_rpi5_examples virtual environment.
Virtual environment exists. Activating...
TAPPAS_POST_PROC_DIR set to /usr/lib/aarch64-linux-gnu/hailo/tappas/post_processes
HailoRT warning: Cannot create log file hailort.log! Please check the file /home/<USER>/.hailo/hailort/hailort.log write permissions.
DEVICE_ARCHITECTURE is set to: HAILO8L
[CONFIG] Using video sink: fakesink
[CONFIG] Set GST_AUTOVIDEOSINK=fakesink for performance mode
HailoRT warning: Cannot create log file hailort.log! Please check the file /home/<USER>/.hailo/hailort/hailort.log write permissions.
[2:49:10.978046844] [137782] [1;32m INFO [1;37mCamera [1;34mcamera_manager.cpp:327 [0mlibcamera v0.4.0+53-29156679
[2:49:10.997327310] [137791] [1;32m INFO [1;37mRPI [1;34mpisp.cpp:720 [0mlibpisp version v1.1.0 e7974a156008 27-01-2025 (21:50:51)
[2:49:10.998808884] [137791] [1;33m WARN [1;37mCameraSensorProperties [1;34mcamera_sensor_properties.cpp:473 [0mNo static properties available for 'imx708_wide_noir'
[2:49:10.998870847] [137791] [1;33m WARN [1;37mCameraSensorProperties [1;34mcamera_sensor_properties.cpp:475 [0mPlease consider updating the camera sensor properties database
[2:49:11.012347571] [137791] [1;33m WARN [1;37mCameraSensor [1;34mcamera_sensor_legacy.cpp:501 [0;32m'imx708_wide_noir': [0mNo sensor delays found in static properties. Assuming unverified defaults.
[2:49:11.013223515] [137791] [1;32m INFO [1;37mRPI [1;34mpisp.cpp:1179 [0mRegistered camera /base/axi/pcie@120000/rp1/i2c@88000/imx708@1a to CFE device /dev/media0 and ISP device /dev/media1 using PiSP variant BCM2712_C0
[2:49:11.018525720] [137782] [1;32m INFO [1;37mCamera [1;34mcamera.cpp:1202 [0mconfiguring streams: (0) 1280x720-RGB888 (1) 1280x720-RGB888 (2) 1536x864-BGGR_PISP_COMP1
[2:49:11.018818868] [137791] [1;32m INFO [1;37mRPI [1;34mpisp.cpp:1484 [0mSensor: /base/axi/pcie@120000/rp1/i2c@88000/imx708@1a - Selected sensor format: 1536x864-SBGGR10_1X10 - Selected CFE format: 1536x864-PC1B
