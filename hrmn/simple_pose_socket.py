#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple Socket.IO server that reads from shared memory and broadcasts pose data.
Includes a Fruit Ninja style game controlled by right hand movements.
OPTIMIZED VERSION for Raspberry Pi 5
"""

import eventlet
eventlet.monkey_patch()

from flask import Flask, send_from_directory
from flask_socketio import Socket<PERSON>
import numpy as np
import time
import threading
import pathlib
import sys

# Import shared memory functions
from shared_memory import open_pose_shm, POSE_SHM_NAME

# Paths
BASE_DIR = pathlib.Path(__file__).parent
STATIC_DIR = BASE_DIR / "static"

# Create Flask app and Socket.IO - OPTIMIZED with logging disabled
app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret!'
socketio = SocketIO(
    app,
    async_mode="eventlet",
    cors_allowed_origins="*",
    logger=False,
    engineio_logger=False)

# Try to connect to shared memory
try:
    print(f"[Server] Connecting to shared memory '{POSE_SHM_NAME}'...")
    shm, poses = open_pose_shm()
    MAX_PERSONS, NUM_KPTS = poses.shape[:2]
    print(f"[Server] Connected to shared memory with shape {poses.shape}")
except FileNotFoundError:
    print(f"[Server] ERROR: Shared memory '{POSE_SHM_NAME}' not found!")
    print(f"[Server] Make sure the writer process is running first.")
    sys.exit(1)

# Socket.IO event handlers
@socketio.on('connect', namespace='/pose')
def handle_connect():
    print('[Socket.IO] Client connected to /pose namespace')

@socketio.on('disconnect', namespace='/pose')
def handle_disconnect():
    print('[Socket.IO] Client disconnected from /pose namespace')

# Broadcaster thread - OPTIMIZED for Raspberry Pi
def broadcast_loop():
    """Read from shared memory and broadcast via Socket.IO"""
    frame_count = 0
    # Pre-allocate buffer for better performance
    buf = np.empty((MAX_PERSONS, NUM_KPTS, 3), dtype=np.float32)

    # Keypoint indices we care about
    keypoint_indices = [0, 5, 7, 9, 6, 8, 10]  # nose, left/right shoulder, elbow, wrist

    while True:
        try:
            # Copy only the data we need instead of the entire array
            np.copyto(buf, poses)

            # Check for valid persons (nose confidence > 0)
            valid_mask = (buf[:, 0, 2] > 0)
            valid_count = int(valid_mask.sum())

            if valid_count > 0:
                # Binary mode - much faster than JSON
                # Extract only the keypoints we need for each valid person
                valid_persons = []
                for idx in range(MAX_PERSONS):
                    if buf[idx, 0, 2] > 0:  # If nose confidence > 0
                        # Extract only the keypoints we need
                        person_data = {
                            "id": idx,
                            "kpts": [buf[idx, kp_idx].tolist() for kp_idx in keypoint_indices]
                        }
                        valid_persons.append(person_data)

                # Emit via Socket.IO
                socketio.emit("pose", valid_persons, namespace="/pose")

            frame_count += 1
            if frame_count % 100 == 0:  # Log less frequently
                print(f"[Socket] Frame {frame_count}: Emitted {valid_count} persons")

            eventlet.sleep(0.05)  # Reduced to 20 FPS for better performance
        except Exception as e:
            print(f"[Socket] Error in broadcast_loop: {e}")
            eventlet.sleep(1.0)  # Sleep longer on error

# Start thread
threading.Thread(target=broadcast_loop, daemon=True).start()

# HTTP routes
@app.route("/")
def root():
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Hand Ninja Game</title>
        <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
        <link rel="icon" href="data:,">
        <style>
            body {
                margin: 0;
                background: #111;
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                flex-direction: column;
                font-family: Arial, sans-serif;
            }
            canvas {
                background: #222;
                margin-bottom: 20px;
                border: 2px solid #444;
            }
            #status {
                color: white;
                font-family: Arial, sans-serif;
                margin-bottom: 10px;
            }
            #score {
                color: white;
                font-size: 24px;
                margin-bottom: 10px;
            }
            #player2-score {
                color: white;
                font-size: 24px;
                margin-bottom: 10px;
            }
            #fps-counter {
                position: absolute;
                top: 20px;
                right: 20px;
                color: white;
                font-size: 18px;
                font-family: Arial, sans-serif;
                background: rgba(0,0,0,0.7);
                padding: 10px;
                border-radius: 5px;
            }
            #debug {
                color: #999;
                font-family: monospace;
                font-size: 12px;
                margin-top: 10px;
                max-width: 640px;
                overflow: auto;
                height: 100px;
            }
            .game-over {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 20px;
                border-radius: 10px;
                text-align: center;
                display: none;
            }
            .game-over button {
                background: #4CAF50;
                border: none;
                color: white;
                padding: 10px 20px;
                text-align: center;
                text-decoration: none;
                display: inline-block;
                font-size: 16px;
                margin: 10px 2px;
                cursor: pointer;
                border-radius: 5px;
            }
            .instructions {
                color: white;
                margin-bottom: 20px;
                text-align: center;
            }
            #fullscreen-btn {
                position: absolute;
                top: 20px;
                left: 20px;
                background: rgba(0,0,0,0.7);
                color: white;
                border: 2px solid #444;
                padding: 10px 15px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 14px;
                font-family: Arial, sans-serif;
            }
            #fullscreen-btn:hover {
                background: rgba(0,0,0,0.9);
                border-color: #666;
            }
            #multiplayer-btn {
                position: absolute;
                top: 70px;
                left: 20px;
                background: rgba(0,0,0,0.7);
                color: white;
                border: 2px solid #444;
                padding: 10px 15px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 14px;
                font-family: Arial, sans-serif;
            }
            #multiplayer-btn:hover {
                background: rgba(0,0,0,0.9);
                border-color: #666;
            }
            .fullscreen-info {
                position: absolute;
                bottom: 20px;
                left: 20px;
                color: #999;
                font-size: 12px;
                font-family: Arial, sans-serif;
            }
        </style>
    </head>
    <body>
        <div id="status">Connecting...</div>
        <button id="fullscreen-btn" onclick="toggleFullscreen()">📺 Fullscreen</button>
        <button id="multiplayer-btn" onclick="toggleMultiplayer()">👥 2 Players</button>
        <div class="instructions">
            <div id="game-title">
                <img id="sensei-logo" src="/Sensei.png" alt="Sensei" style="height: 60px; vertical-align: middle; margin-right: 10px;" onerror="console.log('Sensei image failed to load'); this.alt='[Sensei]'; this.style.border='2px solid red';">
                <h2 style="display: inline; vertical-align: middle;">Hand Ninja</h2>
            </div>
            <p id="game-instructions">Use your hands to touch and slice the fruits! Avoid the bombs!</p>
        </div>
        <div id="timer" style="position: absolute; top: 120px; left: 20px; font-size: 24px; font-weight: bold; color: #ffff00; text-shadow: 2px 2px 4px rgba(0,0,0,0.8);">Time: 60s</div>
        <div id="score">Score: 0</div>
        <div id="player2-score" style="display: none;">Player 2 Score: 0</div>
        <div id="fps-counter">FPS: 0</div>

        <!-- Game Control Panel -->
        <div style="position: absolute; top: 160px; left: 20px; background: rgba(0,0,0,0.7); padding: 15px; border-radius: 10px; color: white; font-family: Arial, sans-serif; min-width: 150px;">
            <div style="font-size: 16px; font-weight: bold; margin-bottom: 10px;">Game Controls</div>
            <div style="margin-bottom: 8px;">Timer: <span id="menu-timer">60s</span></div>
            <div style="margin-bottom: 8px;">High Score: <span id="high-score">0</span></div>
            <button id="reset-btn" onclick="resetGame()" style="background: #ff4444; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; font-size: 14px;">🔄 Reset Game</button>
        </div>
        <canvas id="gameCanvas" width="320" height="240" style="width:640px; height:480px;"></canvas>
        <div id="debug"></div>
        <div class="game-over" id="gameOver">
            <h2>Game Over!</h2>
            <p id="finalScore">Your score: 0</p>
            <button onclick="restartGame()">Play Again</button>
        </div>
        <div class="fullscreen-info">Press F11 or click Fullscreen button</div>

        <script>
            // Game canvas setup - OPTIMIZED
            const canvas = document.getElementById("gameCanvas");
            const ctx = canvas.getContext("2d");
            let W = canvas.width, H = canvas.height;
            // Disable image smoothing for better performance
            ctx.imageSmoothingEnabled = false;
            const statusEl = document.getElementById("status");
            const scoreEl = document.getElementById("score");
            const debugEl = document.getElementById("debug");
            const gameOverEl = document.getElementById("gameOver");
            const finalScoreEl = document.getElementById("finalScore");
            const fpsCounterEl = document.getElementById("fps-counter");

            // Keypoint indices for both arms
            // IMPORTANT: These indices are for the reduced keypoints array we're sending
            const NOSE = 0;
            const LEFT_SHOULDER = 1;
            const LEFT_ELBOW = 2;
            const LEFT_WRIST = 3;
            const RIGHT_SHOULDER = 4;
            const RIGHT_ELBOW = 5;
            const RIGHT_WRIST = 6;

            // Game variables
            let score = 0;
            let gameActive = true;
            let fruits = [];
            let slices = [];

            // Timer-based game variables
            let gameTime = 60; // 60 seconds
            let timeLeft = 60;
            let gameTimer = null;
            let highScore = localStorage.getItem('fruitNinjaHighScore') || 0;

            // Multiplayer variables
            let isMultiplayer = false;
            let player2Score = 0;

            // FPS tracking
            let frameCount = 0;
            let lastFpsTime = Date.now();
            let currentFps = 0;

            // Scaling variables
            const baseWidth = 320;  // Original canvas width
            let scaleFactor = W / baseWidth;

            // Player 1 wrist positions only
            let p1LeftHandPosition = null;
            let p1RightHandPosition = null;
            let p1LeftHandPrevious = null;
            let p1RightHandPrevious = null;

            // Player 2 wrist positions only
            let p2LeftHandPosition = null;
            let p2RightHandPosition = null;
            let p2LeftHandPrevious = null;
            let p2RightHandPrevious = null;

            // Stroke trails for hand movements
            let handStrokes = [];

            // Fruit images
            const fruitTypes = ['apple', 'banana'];  // Use apple and banana
            const fruitImages = {};
            const fruitHalfImages = {};

            // Preload apple image
            const appleImg = new Image();
            appleImg.src = "/apple.png";  // Will be served by Flask
            fruitImages['apple'] = appleImg;

            // Preload banana image
            const bananaImg = new Image();
            bananaImg.src = "/banana.png";  // Will be served by Flask
            fruitImages['banana'] = bananaImg;

            // For simplicity, use the same image for sliced parts
            fruitHalfImages['apple'] = [appleImg, appleImg];
            fruitHalfImages['banana'] = [bananaImg, bananaImg];

            // Load knife image
            const knifeImg = new Image();
            knifeImg.onload = function() {
                console.log("Knife image loaded successfully!");
            };
            knifeImg.onerror = function() {
                console.log("Failed to load knifw.png - using fallback knife shape");
            };
            knifeImg.src = "/knifw.png";  // Will be served by Flask

            // Load background image
            const boardImg = new Image();
            boardImg.onload = function() {
                console.log("Board background loaded successfully!");
            };
            boardImg.onerror = function() {
                console.log("Failed to load board.png - using solid background");
            };
            boardImg.src = "/board.png";  // Will be served by Flask

            // Load sensei logo
            const senseiImg = new Image();
            senseiImg.onload = function() {
                console.log("Sensei logo loaded successfully!");
                // Make sure the HTML img element is visible
                const senseiElement = document.getElementById('sensei-logo');
                if (senseiElement) {
                    senseiElement.style.display = 'inline';
                    console.log("Sensei HTML element made visible");
                }
            };
            senseiImg.onerror = function() {
                console.log("Failed to load Sensei.png - check if file exists");
                // Show placeholder text instead of hiding
                const senseiElement = document.getElementById('sensei-logo');
                if (senseiElement) {
                    senseiElement.alt = '[SENSEI]';
                    senseiElement.style.border = '2px solid red';
                    senseiElement.style.display = 'inline';
                }
            };
            senseiImg.src = "/Sensei.png";  // Will be served by Flask

            // No need to load other fruit images

            // Log to both console and debug element
            function log(msg) {
                console.log(msg);
                debugEl.textContent += msg + "\\n";
                if (debugEl.textContent.split("\\n").length > 10) {
                    const lines = debugEl.textContent.split("\\n");
                    debugEl.textContent = lines.slice(lines.length - 10).join("\\n");
                }
                debugEl.scrollTop = debugEl.scrollHeight;
            }

            // Create Socket.IO connection - extreme optimization
            log("Creating Socket.IO connection...");
            const socket = io("/pose", {
                transports: ["websocket"],  // Force WebSocket only
                upgrade: false,             // Disable transport upgrades
                reconnectionDelay: 100,     // Faster reconnection
                timeout: 5000,              // Shorter timeout
                forceNew: true,             // Force new connection
                perMessageDeflate: false    // Disable compression for lower latency
            });

            socket.on("connect", () => {
                log("Socket connected with ID: " + socket.id);
                statusEl.textContent = "Connected";
                statusEl.style.color = "green";
            });

            socket.on("connect_error", (error) => {
                log("Connection error: " + error);
                statusEl.textContent = "Connection Error";
                statusEl.style.color = "red";
            });

            socket.on("disconnect", (reason) => {
                log("Socket disconnected, reason: " + reason);
                statusEl.textContent = "Disconnected";
                statusEl.style.color = "red";
            });

            // Process pose data - with error handling
            socket.on("pose", data => {
                // Update FPS counter
                frameCount++;
                const now = Date.now();
                if (now - lastFpsTime >= 1000) {
                    currentFps = frameCount;
                    frameCount = 0;
                    lastFpsTime = now;
                    fpsCounterEl.textContent = `FPS: ${currentFps}`;
                }

                if (!gameActive) return;

                if (data.length > 0) {
                    // Process Player 1 (first person)
                    const person1 = data[0];
                    const kpts1 = person1.kpts;

                    if (kpts1 && kpts1.length >= 7) {
                        processPlayerArms(kpts1, 1);
                    }

                    // Process Player 2 (second person) if in multiplayer mode
                    if (isMultiplayer && data.length > 1) {
                        const person2 = data[1];
                        const kpts2 = person2.kpts;

                        if (kpts2 && kpts2.length >= 7) {
                            processPlayerArms(kpts2, 2);
                        }
                    }
                }
            });

            function processPlayerArms(kpts, playerNum) {
                const playerPrefix = playerNum === 1 ? 'p1' : 'p2';
                const screenWidth = isMultiplayer ? W / 2 : W;
                const offsetX = playerNum === 2 ? W / 2 : 0;

                // Process left wrist only
                if (kpts[LEFT_WRIST] && kpts[LEFT_WRIST][2] > 0) {
                    const newLeftHand = {
                        x: kpts[LEFT_WRIST][0] * screenWidth + offsetX,
                        y: kpts[LEFT_WRIST][1] * H
                    };

                    // Add stroke trail if hand moved
                    const prevHand = window[playerPrefix + 'LeftHandPrevious'];
                    if (prevHand) {
                        const distance = Math.sqrt(
                            Math.pow(newLeftHand.x - prevHand.x, 2) +
                            Math.pow(newLeftHand.y - prevHand.y, 2)
                        );

                        // Only add stroke if hand moved significantly
                        if (distance > 5) {
                            handStrokes.push({
                                x1: prevHand.x,
                                y1: prevHand.y,
                                x2: newLeftHand.x,
                                y2: newLeftHand.y,
                                time: Date.now(),
                                player: playerNum,
                                hand: 'left'
                            });
                        }
                    }

                    window[playerPrefix + 'LeftHandPrevious'] = window[playerPrefix + 'LeftHandPosition'];
                    window[playerPrefix + 'LeftHandPosition'] = newLeftHand;

                    // Check collisions
                    checkKnifeCollisions(newLeftHand, null, null, playerNum, offsetX, screenWidth);
                } else {
                    window[playerPrefix + 'LeftHandPosition'] = null;
                }

                // Process right wrist only
                if (kpts[RIGHT_WRIST] && kpts[RIGHT_WRIST][2] > 0) {
                    const newRightHand = {
                        x: kpts[RIGHT_WRIST][0] * screenWidth + offsetX,
                        y: kpts[RIGHT_WRIST][1] * H
                    };

                    // Add stroke trail if hand moved
                    const prevHand = window[playerPrefix + 'RightHandPrevious'];
                    if (prevHand) {
                        const distance = Math.sqrt(
                            Math.pow(newRightHand.x - prevHand.x, 2) +
                            Math.pow(newRightHand.y - prevHand.y, 2)
                        );

                        // Only add stroke if hand moved significantly
                        if (distance > 5) {
                            handStrokes.push({
                                x1: prevHand.x,
                                y1: prevHand.y,
                                x2: newRightHand.x,
                                y2: newRightHand.y,
                                time: Date.now(),
                                player: playerNum,
                                hand: 'right'
                            });
                        }
                    }

                    window[playerPrefix + 'RightHandPrevious'] = window[playerPrefix + 'RightHandPosition'];
                    window[playerPrefix + 'RightHandPosition'] = newRightHand;

                    // Check collisions
                    checkKnifeCollisions(newRightHand, null, null, playerNum, offsetX, screenWidth);
                } else {
                    window[playerPrefix + 'RightHandPosition'] = null;
                }
            }

            // Game functions
            function createFruit() {
                if (!gameActive) return;

                const isBomb = Math.random() < 0.1;  // 10% chance of bomb

                // Scale fruit size based on screen size (limited scaling for fullscreen)
                const baseAppleRadius = 20;
                const baseBombRadius = 15;
                const fruitScale = Math.min(scaleFactor, 3); // Limit fruit scaling to max 3x
                const appleRadius = Math.round(baseAppleRadius * fruitScale);
                const bombRadius = Math.round(baseBombRadius * fruitScale);

                // Choose random fruit type if not bomb
                const fruitType = isBomb ? 'bomb' : fruitTypes[Math.floor(Math.random() * fruitTypes.length)];

                // Calculate physics to reach 70% of screen height
                const targetHeight = H * 0.7;  // 70% of screen height
                const baseGravity = 0.2;  // Base gravity for normal screen
                const gravity = baseGravity * Math.min(scaleFactor, 2);  // Scale gravity for fullscreen

                // Calculate initial velocity needed to reach target height
                // Using physics: v² = 2gh, so v = sqrt(2 * g * h)
                const baseVelocity = Math.sqrt(2 * gravity * targetHeight);
                const velocityVariation = baseVelocity * 0.2;  // ±20% variation

                // Launch from random positions across the bottom
                const launchX = Math.random() * (W - 100) + 50;  // Random position, 50px from edges

                const fruit = {
                    x: launchX,  // Launch from random bottom positions
                    y: H + 30,  // Start just below screen (fixed distance)
                    type: fruitType,
                    radius: isBomb ? bombRadius : appleRadius,  // Scaled fruit size
                    velocityX: (Math.random() - 0.5) * 4 * fruitScale,  // Random horizontal movement (-2 to +2)
                    velocityY: -(baseVelocity + (Math.random() - 0.5) * velocityVariation),  // Calculated velocity for 70% height
                    rotation: Math.random() * 360,
                    rotationSpeed: (Math.random() - 0.5) * 5,
                    sliced: false,
                    sliceTime: 0,
                    gravity: gravity  // Fixed gravity value
                };

                fruits.push(fruit);

                // Schedule next fruit
                const delay = 1000 + Math.random() * 1500;  // Longer delay between fruits
                setTimeout(createFruit, delay);
            }

            // Check if wrist point collides with any fruits
            function checkKnifeCollisions(handPos, elbowPos, shoulderPos, playerNum = 1, offsetX = 0, screenWidth = W) {
                fruits.forEach(fruit => {
                    if (fruit.sliced) return;

                    // Calculate distance from wrist to fruit center
                    const dx = fruit.x - handPos.x;
                    const dy = fruit.y - handPos.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    // Check if wrist point is touching the fruit
                    const touchRadius = 15 * Math.min(scaleFactor, 1.5); // Same as hand circle size
                    if (distance < fruit.radius + touchRadius) {
                        // Slice the fruit
                        fruit.sliced = true;
                        fruit.sliceTime = Date.now();

                        // Add slice effect at the contact point
                        const sliceX = handPos.x;
                        const sliceY = handPos.y;

                        // Add slice effect (scaled)
                        const sliceSize = 30 * scaleFactor;
                        slices.push({
                            x1: sliceX - sliceSize, y1: sliceY - sliceSize,
                            x2: sliceX + sliceSize, y2: sliceY + sliceSize,
                            time: Date.now()
                        });

                        // Update score for the correct player
                        if (fruit.type === 'bomb') {
                            // Bomb penalty: lose 20 points
                            if (playerNum === 1) {
                                score = Math.max(0, score - 20);
                                if (isMultiplayer) {
                                    scoreEl.textContent = "Player 1 Score: " + score;
                                } else {
                                    scoreEl.textContent = "Score: " + score;
                                }
                            } else {
                                player2Score = Math.max(0, player2Score - 20);
                                document.getElementById('player2-score').textContent = "Player 2 Score: " + player2Score;
                            }
                        } else {
                            // Fruit reward: gain 10 points
                            if (playerNum === 1) {
                                score += 10;
                                if (isMultiplayer) {
                                    scoreEl.textContent = "Player 1 Score: " + score;
                                } else {
                                    scoreEl.textContent = "Score: " + score;
                                }
                            } else {
                                player2Score += 10;
                                document.getElementById('player2-score').textContent = "Player 2 Score: " + player2Score;
                            }
                        }
                    }
                });
            }

            function startGameTimer() {
                timeLeft = gameTime;
                updateTimerDisplay();

                gameTimer = setInterval(() => {
                    timeLeft--;
                    updateTimerDisplay();

                    if (timeLeft <= 0) {
                        gameOver();
                    }
                }, 1000);
            }

            function updateTimerDisplay() {
                const timerEl = document.getElementById('timer');
                const menuTimerEl = document.getElementById('menu-timer');
                const timeText = timeLeft + "s";

                if (timerEl) {
                    timerEl.textContent = "Time: " + timeText;

                    // Change color based on time left
                    if (timeLeft <= 10) {
                        timerEl.style.color = "#ff4444";
                    } else if (timeLeft <= 20) {
                        timerEl.style.color = "#ffaa00";
                    } else {
                        timerEl.style.color = "#ffff00";
                    }
                }

                if (menuTimerEl) {
                    menuTimerEl.textContent = timeText;
                }
            }

            function gameOver() {
                gameActive = false;
                if (gameTimer) {
                    clearInterval(gameTimer);
                    gameTimer = null;
                }

                // Check for high score
                const finalScore = isMultiplayer ? Math.max(score, player2Score) : score;
                if (finalScore > highScore) {
                    highScore = finalScore;
                    localStorage.setItem('fruitNinjaHighScore', highScore);
                    const highScoreEl = document.getElementById('high-score');
                    if (highScoreEl) {
                        highScoreEl.textContent = highScore;
                    }
                    finalScoreEl.textContent = "NEW HIGH SCORE: " + finalScore;
                } else {
                    finalScoreEl.textContent = "Your score: " + finalScore;
                }

                gameOverEl.style.display = "block";
            }

            function restartGame() {
                score = 0;
                player2Score = 0;
                fruits = [];
                slices = [];
                gameActive = true;

                // Reset timer
                if (gameTimer) {
                    clearInterval(gameTimer);
                }
                startGameTimer();

                if (isMultiplayer) {
                    scoreEl.textContent = "Player 1 Score: " + score;
                    document.getElementById('player2-score').textContent = "Player 2 Score: " + player2Score;
                } else {
                    scoreEl.textContent = "Score: " + score;
                }

                gameOverEl.style.display = "none";
                createFruit();
            }

            function resetGame() {
                restartGame();
            }

            // Optimize update function
            function update() {
                // Request next frame immediately for smoother animation
                requestAnimationFrame(update);

                // Optimized rendering - only clear the canvas once
                ctx.clearRect(0, 0, W, H);

                // Draw background - use board.png or fallback to solid color
                if (boardImg.complete) {
                    // Draw the board background image scaled to fit
                    ctx.drawImage(boardImg, 0, 0, W, H);
                } else {
                    // Fallback to solid color
                    ctx.fillStyle = "#222";
                    ctx.fillRect(0, 0, W, H);
                }

                // Draw split screen divider in multiplayer mode
                if (isMultiplayer) {
                    ctx.strokeStyle = "#666";
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(W / 2, 0);
                    ctx.lineTo(W / 2, H);
                    ctx.stroke();

                    // Draw player labels
                    ctx.fillStyle = "#fff";
                    ctx.font = "20px Arial";
                    ctx.textAlign = "center";
                    ctx.fillText("Player 1", W / 4, 30);
                    ctx.fillText("Player 2", (3 * W) / 4, 30);
                }

                // Draw Player 1 arms
                drawPlayerArms(1);

                // Draw Player 2 arms (if multiplayer)
                if (isMultiplayer) {
                    drawPlayerArms(2);
                }

                // Update and draw fruits
                for (let i = fruits.length - 1; i >= 0; i--) {
                    const fruit = fruits[i];

                    // Update position
                    fruit.x += fruit.velocityX;
                    fruit.y += fruit.velocityY;
                    fruit.velocityY += fruit.gravity;
                    fruit.rotation += fruit.rotationSpeed;

                    // Remove if off screen (either top or bottom)
                    if (fruit.y > H + 100 || fruit.y < -100) {
                        // No penalty for missed fruits in timer-based game
                        fruits.splice(i, 1);
                        continue;
                    }

                    // Draw fruit
                    ctx.save();
                    ctx.translate(fruit.x, fruit.y);
                    ctx.rotate(fruit.rotation * Math.PI / 180);

                    if (fruit.type === 'bomb') {
                        // Draw bomb as a black circle
                        ctx.fillStyle = "#000";
                        ctx.beginPath();
                        ctx.arc(0, 0, fruit.radius, 0, Math.PI * 2);
                        ctx.fill();

                        // Add a fuse
                        ctx.strokeStyle = "#f80";
                        ctx.lineWidth = 3;
                        ctx.beginPath();
                        ctx.moveTo(0, -fruit.radius);
                        ctx.lineTo(0, -fruit.radius - 15);
                        ctx.stroke();

                        // Add a spark
                        ctx.fillStyle = "#ff0";
                        ctx.beginPath();
                        ctx.arc(0, -fruit.radius - 15, 5, 0, Math.PI * 2);
                        ctx.fill();
                    } else {
                        // Draw fruit (apple or banana)
                        const fruitImg = fruitImages[fruit.type];
                        if (fruitImg && fruitImg.complete) {
                            if (!fruit.sliced) {
                                ctx.drawImage(fruitImg, -fruit.radius, -fruit.radius, fruit.radius * 2, fruit.radius * 2);
                            } else {
                                // For sliced fruit, draw two halves with offset
                                const timeSinceSlice = Date.now() - fruit.sliceTime;
                                const offset = timeSinceSlice * 0.1;

                                ctx.save();
                                ctx.translate(-offset, 0);
                                ctx.drawImage(fruitImg, -fruit.radius, -fruit.radius, fruit.radius, fruit.radius * 2);
                                ctx.restore();

                                ctx.save();
                                ctx.translate(offset, 0);
                                ctx.drawImage(fruitImg, 0, -fruit.radius, fruit.radius, fruit.radius * 2);
                                ctx.restore();
                            }
                        } else {
                            // Fallback: draw colored circle if image not loaded
                            ctx.fillStyle = fruit.type === 'apple' ? "#ff0000" : "#ffff00";
                            ctx.beginPath();
                            ctx.arc(0, 0, fruit.radius, 0, Math.PI * 2);
                            ctx.fill();
                        }
                    }

                    ctx.restore();
                }

                // Draw hand stroke trails
                for (let i = handStrokes.length - 1; i >= 0; i--) {
                    const stroke = handStrokes[i];
                    const age = Date.now() - stroke.time;

                    if (age > 1000) {  // Strokes last 1 second
                        handStrokes.splice(i, 1);
                        continue;
                    }

                    const alpha = 1 - age / 1000;
                    const strokeColor = stroke.player === 1 ?
                        (stroke.hand === 'left' ? "#5af" : "#f5a") :
                        (stroke.hand === 'left' ? "#5f5" : "#fa5");

                    ctx.strokeStyle = strokeColor.replace('#', 'rgba(') + alpha + ')';
                    ctx.lineWidth = 4 * Math.min(scaleFactor, 2);
                    ctx.lineCap = "round";
                    ctx.beginPath();
                    ctx.moveTo(stroke.x1, stroke.y1);
                    ctx.lineTo(stroke.x2, stroke.y2);
                    ctx.stroke();
                }

                // Draw slices (scaled)
                for (let i = slices.length - 1; i >= 0; i--) {
                    const slice = slices[i];
                    const age = Date.now() - slice.time;

                    if (age > 500) {
                        slices.splice(i, 1);
                        continue;
                    }

                    const alpha = 1 - age / 500;
                    ctx.strokeStyle = "rgba(255, 255, 255, " + alpha + ")";
                    ctx.lineWidth = 3 * scaleFactor;
                    ctx.beginPath();
                    ctx.moveTo(slice.x1, slice.y1);
                    ctx.lineTo(slice.x2, slice.y2);
                    ctx.stroke();
                }

                // Lives are no longer used in timer-based game

                // No need to reference lastPosition anymore since we have rightHandPosition and leftHandPosition
            }

            // Function to draw player wrist points only
            function drawPlayerArms(playerNum) {
                const prefix = playerNum === 1 ? 'p1' : 'p2';
                const leftHand = window[prefix + 'LeftHandPosition'];
                const rightHand = window[prefix + 'RightHandPosition'];

                const leftColor = playerNum === 1 ? "#5af" : "#5f5";  // Blue for P1, Green for P2
                const rightColor = playerNum === 1 ? "#f5a" : "#fa5"; // Pink for P1, Orange for P2

                const pointRadius = 8 * Math.min(scaleFactor, 1.5);

                // Draw left wrist point
                if (leftHand) {
                    ctx.fillStyle = leftColor;
                    ctx.beginPath();
                    ctx.arc(leftHand.x, leftHand.y, pointRadius, 0, Math.PI * 2);
                    ctx.fill();

                    // Add white border for visibility
                    ctx.strokeStyle = "#fff";
                    ctx.lineWidth = 2;
                    ctx.stroke();
                }

                // Draw right wrist point
                if (rightHand) {
                    ctx.fillStyle = rightColor;
                    ctx.beginPath();
                    ctx.arc(rightHand.x, rightHand.y, pointRadius, 0, Math.PI * 2);
                    ctx.fill();

                    // Add white border for visibility
                    ctx.strokeStyle = "#fff";
                    ctx.lineWidth = 2;
                    ctx.stroke();
                }
            }

            // Function to draw knife at wrist
            function drawKnife(handPos, elbowPos, color) {
                const knifeLength = 40 * scaleFactor;
                const knifeWidth = 20 * scaleFactor;

                // Calculate direction from elbow to hand for knife orientation
                const dx = handPos.x - elbowPos.x;
                const dy = handPos.y - elbowPos.y;
                const length = Math.sqrt(dx*dx + dy*dy);

                if (length > 0) {
                    // Normalize direction vector
                    const dirX = dx / length;
                    const dirY = dy / length;

                    // Calculate knife center position (extending from wrist)
                    const knifeCenterX = handPos.x + dirX * (knifeLength / 2);
                    const knifeCenterY = handPos.y + dirY * (knifeLength / 2);

                    // Calculate rotation angle
                    const angle = Math.atan2(dy, dx);

                    // Draw knife PNG image
                    ctx.save();
                    ctx.translate(knifeCenterX, knifeCenterY);
                    ctx.rotate(angle);

                    // Draw the knife image centered at the position
                    if (knifeImg.complete) {
                        // Use the knife PNG image
                        ctx.drawImage(knifeImg, -knifeLength/2, -knifeWidth/2, knifeLength, knifeWidth);
                    } else {
                        // Fallback: Draw a more knife-like shape instead of simple line
                        ctx.fillStyle = "#C0C0C0";  // Silver blade
                        ctx.beginPath();
                        // Draw knife blade shape
                        ctx.moveTo(-knifeLength/2, -2);
                        ctx.lineTo(knifeLength/3, -2);
                        ctx.lineTo(knifeLength/2, 0);
                        ctx.lineTo(knifeLength/3, 2);
                        ctx.lineTo(-knifeLength/2, 2);
                        ctx.closePath();
                        ctx.fill();

                        // Draw handle
                        ctx.fillStyle = "#8B4513";  // Brown handle
                        ctx.fillRect(-knifeLength/2 - 10, -3, 10, 6);
                    }

                    ctx.restore();
                }
            }

            // Multiplayer functionality
            function toggleMultiplayer() {
                isMultiplayer = !isMultiplayer;
                const btn = document.getElementById('multiplayer-btn');
                const instructions = document.getElementById('game-instructions');
                const player2ScoreEl = document.getElementById('player2-score');

                if (isMultiplayer) {
                    btn.textContent = '👤 1 Player';
                    instructions.textContent = 'Player 1 (left side) and Player 2 (right side) - 1 minute to score!';
                    player2ScoreEl.style.display = 'block';

                    // Reset scores for both players
                    score = 0;
                    player2Score = 0;
                    scoreEl.textContent = "Player 1 Score: " + score;
                    player2ScoreEl.textContent = "Player 2 Score: " + player2Score;
                } else {
                    btn.textContent = '👥 2 Players';
                    instructions.textContent = 'Use your hands to slice fruits! 1 minute timer - avoid bombs!';
                    player2ScoreEl.style.display = 'none';
                    scoreEl.textContent = "Score: " + score;
                }

                // Restart the game with new mode
                restartGame();
            }

            // Fullscreen functionality
            function toggleFullscreen() {
                if (!document.fullscreenElement) {
                    document.documentElement.requestFullscreen().then(() => {
                        resizeCanvas();
                        document.getElementById('fullscreen-btn').textContent = '🪟 Exit Fullscreen';
                    }).catch(err => {
                        console.log('Error attempting to enable fullscreen:', err);
                    });
                } else {
                    document.exitFullscreen().then(() => {
                        resizeCanvas();
                        document.getElementById('fullscreen-btn').textContent = '📺 Fullscreen';
                    });
                }
            }

            function resizeCanvas() {
                if (document.fullscreenElement) {
                    // Fullscreen mode - use entire screen
                    canvas.width = window.innerWidth;
                    canvas.height = window.innerHeight;
                    canvas.style.width = window.innerWidth + 'px';
                    canvas.style.height = window.innerHeight + 'px';
                } else {
                    // Windowed mode - use original size
                    canvas.width = 320;
                    canvas.height = 240;
                    canvas.style.width = '640px';
                    canvas.style.height = '480px';
                }

                // Update canvas dimensions for game logic
                W = canvas.width;
                H = canvas.height;

                // Update scale factor
                scaleFactor = W / baseWidth;

                // Disable image smoothing after resize
                ctx.imageSmoothingEnabled = false;
            }

            // Listen for fullscreen changes
            document.addEventListener('fullscreenchange', () => {
                if (!document.fullscreenElement) {
                    resizeCanvas();
                    document.getElementById('fullscreen-btn').textContent = '📺 Fullscreen';
                }
            });

            // Listen for F11 key
            document.addEventListener('keydown', (event) => {
                if (event.key === 'F11') {
                    event.preventDefault();
                    toggleFullscreen();
                }
            });

            // Listen for Escape key to exit fullscreen
            document.addEventListener('keydown', (event) => {
                if (event.key === 'Escape' && document.fullscreenElement) {
                    document.exitFullscreen();
                }
            });

            // Handle window resize
            window.addEventListener('resize', () => {
                if (document.fullscreenElement) {
                    resizeCanvas();
                }
            });

            // Initialize high score display
            const highScoreEl = document.getElementById('high-score');
            if (highScoreEl) {
                highScoreEl.textContent = highScore;
            }

            // Start the game
            startGameTimer();
            createFruit();
            update();

            // Additional debugging
            socket.io.on("error", (error) => {
                log("Transport error: " + error);
            });

            socket.io.on("reconnect_attempt", (attempt) => {
                log("Reconnection attempt: " + attempt);
            });

            // Helper function to calculate distance from point to line segment
            function distanceToLineSegment(px, py, x1, y1, x2, y2) {
                const A = px - x1;
                const B = py - y1;
                const C = x2 - x1;
                const D = y2 - y1;

                const dot = A * C + B * D;
                const len_sq = C * C + D * D;
                let param = -1;

                if (len_sq !== 0) {
                    param = dot / len_sq;
                }

                let xx, yy;

                if (param < 0) {
                    xx = x1;
                    yy = y1;
                } else if (param > 1) {
                    xx = x2;
                    yy = y2;
                } else {
                    xx = x1 + param * C;
                    yy = y1 + param * D;
                }

                const dx = px - xx;
                const dy = py - yy;

                return Math.sqrt(dx * dx + dy * dy);
            }

            // Helper function to find closest point on line segment
            function closestPointOnLine(px, py, x1, y1, x2, y2) {
                const A = px - x1;
                const B = py - y1;
                const C = x2 - x1;
                const D = y2 - y1;

                const dot = A * C + B * D;
                const len_sq = C * C + D * D;
                let param = -1;

                if (len_sq !== 0) {
                    param = dot / len_sq;
                }

                if (param < 0) {
                    return {x: x1, y: y1};
                } else if (param > 1) {
                    return {x: x2, y: y2};
                } else {
                    return {
                        x: x1 + param * C,
                        y: y1 + param * D
                    };
                }
            }
        </script>
    </body>
    </html>
    """

@app.route("/<path:filename>")
def static_file(filename):
    # First try to serve from static directory
    try:
        return send_from_directory(STATIC_DIR, filename)
    except:
        # If not found, try to serve from the image directory
        image_dir = pathlib.Path("/home/<USER>/Documents/taha_ai/hrmn")
        return send_from_directory(image_dir, filename)

if __name__ == "__main__":
    print(f"[Server] Starting Hand Ninja Game server on http://0.0.0.0:5000")
    print(f"[Server] OPTIMIZED VERSION for Raspberry Pi 5")
    try:
        # Turn off debug mode for better performance
        socketio.run(app, host="0.0.0.0", port=5000, debug=False)
    except Exception as e:
        print(f"[Server] Error starting server: {e}")
    finally:
        shm.close()
