#!/bin/bash

# =============================================================================
# Simple Raspberry Pi Pose Estimation & Hand Ninja Game Startup Script
# =============================================================================

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}[STARTUP]${NC} Starting Raspberry Pi Pose Estimation System"
echo ""

# 1) ALWAYS stop existing processes first (automatic cleanup)
echo -e "${BLUE}[CLEANUP]${NC} Stopping any existing processes..."
pkill -f "pose_estimation.py" 2>/dev/null && echo -e "${GREEN}[INFO]${NC} Stopped pose estimation" || true
pkill -f "hand_pose_estimation.py" 2>/dev/null && echo -e "${GREEN}[INFO]${NC} Stopped hand pose estimation" || true
pkill -f "simple_pose_socket.py" 2>/dev/null && echo -e "${GREEN}[INFO]${NC} Stopped web server" || true
pkill -f "hand_ninja_game.py" 2>/dev/null && echo -e "${GREEN}[INFO]${NC} Stopped hand ninja game" || true

# Clean up shared memory
rm -f /dev/shm/pose_shm 2>/dev/null && echo -e "${GREEN}[INFO]${NC} Cleaned shared memory" || true

echo -e "${GREEN}[INFO]${NC} Cleanup completed"
echo ""

# 2) Start backend
echo -e "${BLUE}[BACKEND]${NC} Starting pose estimation backend..."
nohup ./run_pose_estimation.sh --input rpi > backend.log 2>&1 &
BACKEND_PID=$!
echo -e "${GREEN}[INFO]${NC} Backend started with PID: $BACKEND_PID"

# 3) Wait for shared memory
echo -e "${BLUE}[WAIT]${NC} Waiting for shared memory to be created..."
for i in {1..30}; do
    if [ -f "/dev/shm/pose_shm" ]; then
        echo -e "${GREEN}[INFO]${NC} Shared memory created successfully"
        break
    fi
    if [ $i -eq 30 ]; then
        echo -e "${YELLOW}[WARN]${NC} Timeout waiting for shared memory. Check backend.log"
        echo "Backend log:"
        tail -10 backend.log
        exit 1
    fi
    sleep 1
    echo -n "."
done
echo ""

# 4) Start frontend
echo -e "${BLUE}[FRONTEND]${NC} Starting web server..."
nohup python simple_pose_socket.py > frontend.log 2>&1 &
FRONTEND_PID=$!
echo -e "${GREEN}[INFO]${NC} Frontend started with PID: $FRONTEND_PID"

# 5) Wait for frontend to start
sleep 3

# 6) Open browser automatically
echo -e "${BLUE}[BROWSER]${NC} Opening web browser..."
if command -v xdg-open > /dev/null; then
    xdg-open http://localhost:5000 > /dev/null 2>&1 &
    echo -e "${GREEN}[INFO]${NC} Browser opened automatically"
elif command -v chromium-browser > /dev/null; then
    chromium-browser http://localhost:5000 > /dev/null 2>&1 &
    echo -e "${GREEN}[INFO]${NC} Chromium browser opened automatically"
else
    echo -e "${YELLOW}[WARN]${NC} No browser found - please open http://localhost:5000 manually"
fi

# 7) Final status
echo ""
echo -e "${BLUE}[SUCCESS]${NC} Startup Complete!"
echo -e "${GREEN}[INFO]${NC} Backend PID: $BACKEND_PID"
echo -e "${GREEN}[INFO]${NC} Frontend PID: $FRONTEND_PID"
echo -e "${GREEN}[INFO]${NC} Web interface: http://localhost:5000"
echo -e "${GREEN}[INFO]${NC} Logs: backend.log, frontend.log"
echo ""
echo -e "${YELLOW}[TIP]${NC} Use 'pkill -f pose_estimation.py && pkill -f simple_pose_socket.py' to stop"
echo -e "${YELLOW}[TIP]${NC} Use 'tail -f backend.log' or 'tail -f frontend.log' to monitor"
