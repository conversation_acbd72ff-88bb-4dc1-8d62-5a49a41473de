#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gi, signal, sys, cv2, hailo
gi.require_version('Gst', '1.0')
from gi.repository import Gst, GLib

# ----------------------------------------------------------------------
# Hailo
# ----------------------------------------------------------------------
from hailo_apps_infra.hailo_rpi_common import (
    get_caps_from_pad,
    get_numpy_from_buffer,
    app_callback_class,
)
from hailo_apps_infra.pose_estimation_pipeline import GStreamerPoseEstimationApp

# ----------------------------------------------------------------------
# Shared Memory - nur für Hände
# ----------------------------------------------------------------------
from hand_shared_memory import init_hand_shm, HAND_SHM_NAME

MAX_PERSONS = 10
# Indizes für die Handgelenke im ursprünglichen Pose-Array
LEFT_WRIST_IDX = 9
RIGHT_WRIST_IDX = 10

hand_shm, hand_arr = init_hand_shm(MAX_PERSONS)
# shape = (MAX_PERSONS, 2, 3) - 2 Hände (links/rechts), 3 Werte (x, y, conf)

# ----------------------------------------------------------------------
class UserAppCallback(app_callback_class):
    def __init__(self):
        super().__init__()

def app_callback(pad, info, user_data):
    buf = info.get_buffer()
    if not buf:
        return Gst.PadProbeReturn.OK

    # Nur die Handpositionen zurücksetzen
    hand_arr.fill(-1.0)

    user_data.increment()

    # Vorschau deaktivieren - keine Frame-Kopien mehr
    user_data.use_frame = False

    # Nur die notwendigen Daten extrahieren, ohne Frame-Kopie
    import hailo
    roi = hailo.get_roi_from_buffer(buf)
    detections = roi.get_objects_typed(hailo.HAILO_DETECTION)

    person_idx = 0
    for det in detections:
        if det.get_label() != "person":
            continue
        if person_idx >= MAX_PERSONS:
            break

        lms = det.get_objects_typed(hailo.HAILO_LANDMARKS)
        if not lms:
            continue
        points = lms[0].get_points()
        bb = det.get_bbox()  # normalisierte BBox

        # Nur die Handgelenke extrahieren
        if len(points) > max(LEFT_WRIST_IDX, RIGHT_WRIST_IDX):
            # Linkes Handgelenk (Index 0 in unserem neuen Array)
            left_wrist = points[LEFT_WRIST_IDX]
            x_norm = left_wrist.x() * bb.width() + bb.xmin()
            y_norm = left_wrist.y() * bb.height() + bb.ymin()
            conf = getattr(left_wrist, "score", lambda:1.)()
            hand_arr[person_idx, 0] = (x_norm, y_norm, conf)

            # Rechtes Handgelenk (Index 1 in unserem neuen Array)
            right_wrist = points[RIGHT_WRIST_IDX]
            x_norm = right_wrist.x() * bb.width() + bb.xmin()
            y_norm = right_wrist.y() * bb.height() + bb.ymin()
            conf = getattr(right_wrist, "score", lambda:1.)()
            hand_arr[person_idx, 1] = (x_norm, y_norm, conf)

            # Kein Overlay mehr - spart CPU-Zeit

        person_idx += 1

    # Keine Frame-Verarbeitung mehr nötig
    return Gst.PadProbeReturn.OK

# ----------------------------------------------------------------------
def cleanup(*_):
    print("\n[INFO] Kill hand_pose_estimation.py – unlink shm …")
    hand_shm.close()
    hand_shm.unlink()
    sys.exit(0)

signal.signal(signal.SIGINT, cleanup)

# ----------------------------------------------------------------------
if __name__ == "__main__":
    print(f"[INFO] Schreibe Hand-Daten in SHM='{HAND_SHM_NAME}' - OPTIMIERT MIT FAKESINK")
    user_data = UserAppCallback()
    # Vorschau explizit deaktivieren
    user_data.use_frame = False

    # MONKEY-PATCH: autovideosink → fakesink global ersetzen
    import gi
    gi.require_version('Gst', '1.0')
    from gi.repository import Gst

    # Originale ElementFactory-Funktion sichern
    original_make = Gst.ElementFactory.make

    def patched_make(factory_name, element_name=None):
        # Wenn autovideosink angefordert wird, gib fakesink zurück
        if factory_name == "autovideosink":
            print(f"[FAKESINK-PATCH] autovideosink → fakesink umgeleitet!")
            return original_make("fakesink", element_name)
        return original_make(factory_name, element_name)

    # Monkey-Patch anwenden
    Gst.ElementFactory.make = patched_make
    print("[FAKESINK-PATCH] GStreamer ElementFactory gepatcht für fakesink!")

    # Pipeline ohne preview Parameter erstellen
    app = GStreamerPoseEstimationApp(app_callback, user_data)

    # Check if camera_id argument is provided
    import sys
    if len(sys.argv) > 1 and "--camera-id" in sys.argv:
        camera_id_idx = sys.argv.index("--camera-id")
        if camera_id_idx + 1 < len(sys.argv):
            camera_id = int(sys.argv[camera_id_idx + 1])
            print(f"[INFO] Using camera ID: {camera_id}")
            # Set environment variable for camera selection
            import os
            os.environ['CAMERA_ID'] = str(camera_id)

    try:
        app.run()
    finally:
        cleanup()
